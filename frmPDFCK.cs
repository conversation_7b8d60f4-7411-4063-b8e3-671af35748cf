using AIServices;
using AIServices.Models.Chat;
using AIServices.Utils;
using HIH.Framework.BaseUIDX;
using HIH.Framework.Common.Data;
using Newtonsoft.Json;
using Spire.Pdf;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.CMS.Main
{
    /// <summary>
    /// 出口保税核注清单处理窗体
    /// </summary>
    public partial class frmPDFCK : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        #region 字段和属性

        // AI服务相关
        private readonly Ai ai;
        private readonly ChatRequest chatRequest;
        private readonly TaskSequenceHelper task;

        // PDF处理相关
        private string currentFilePath = "";
        private int currentPageIndex = 0;  // 当前处理的页码索引
        private int totalPages = 0;        // PDF总页数
        private bool isEnd = false;

        // 数据相关
        private List<dynamic> allDetails = new List<dynamic>(); // 存储所有明细数据
        private DataTable dt;              // 主表数据存储
        private DataTable detailDT;        // 明细表数据存储
        private DataTable initialDetailDT; // 初始明细表数据存储
        private bool isSave = false;       // true:已经保存，false:新建

        // 计时相关
        private DateTime startTime;
        private TimeSpan elapsedTime;
        private bool isTimerRunning = false;

        //币种
        private string jkBZ = "";
        private string ckBZ = "";
        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public frmPDFCK()
        {
            InitializeComponent();
            //isLoadPerm = false;

            // 初始化AI服务
            ai = new Ai("sk-66716486ee3148cc8160148cfe07b41a", "cebfadc871fd4b1ea2ba7e0d85646e79");
            task = new TaskSequenceHelper("473b94afc1c64badb8eb228dc7e90663");
            chatRequest = new ChatRequest() { Temperature = 0.1 };
        }


        private async void btnSelectFile_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // 选择文件
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PDF文件|*.pdf",
                Title = "选择PDF文件"
            };
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                Clear();
                try
                {
                    allDetails.Clear();
                    // 保存文件路径
                    string filePath = openFileDialog.FileName;
                    currentFilePath = filePath;

                    // 获取PDF总页数
                    totalPages = GetPdfPageCount(filePath);

                    // 重置当前页码索引
                    currentPageIndex = 0;
                    isEnd = false;

                    ShowWaitForm.WaitFormShow(this, "正在提取...");

                    // 开始计时
                    StartTimer();

                    // 提取第一页内容，检查是否是进口保税核注清单
                    string firstPageContent = ExtractPdfPageContent(currentFilePath, 0).ToString();
                    if (!firstPageContent.Contains("出口保税核注清单"))
                    {
                        throw new Exception("当前识别的PDF不是 '出口保税核注清单' !");
                    }

                    // 使用批量并发请求处理
                    await ProcessPdfWithBatchRequestsAsync(firstPageContent);
                }
                catch (Exception ex)
                {
                    // 停止计时
                    StopTimer();
                    ShowWaitForm.SetWaitFormStop(ex.Message);
                }
            }
        }


        #region PDF处理与AI识别

        /// <summary>
        /// 使用批量并发请求处理PDF，实现按顺序实时显示结果
        /// </summary>
        /// <param name="firstPageContent">第一页PDF内容</param>
        private async Task ProcessPdfWithBatchRequestsAsync(string firstPageContent)
        {
            try
            {
                // 设置最大并发请求数
                const int maxConcurrentRequests = 5; // 稍微降低并发数，提高稳定性

                // 准备所有页面的请求
                List<ChatRequest> requests = PrepareAllRequests(firstPageContent);
                int totalRequests = requests.Count;

                if (totalRequests == 0)
                {
                    ShowWaitForm.SetWaitFormStop("未能提取有效内容，请检查PDF文件");
                    return;
                }

                ShowWaitForm.SetWaitFormDesption($"开始识别...共{totalRequests}个");

                // 初始化结果处理所需的数据结构
                var processingData = InitializeProcessingData(totalRequests);

                // 创建请求队列
                var requestQueue = new ConcurrentQueue<(int index, ChatRequest request)>(
                    requests.Select((req, idx) => (idx, req)));

                // 创建 SequentialTaskContext
                var taskContext = new AIServices.Utils.SequentialTaskContext(processingData.CompletionSources);

                // 使用 TaskSequenceHelper 设置顺序回调处理结果
                task.SetupSequentialCallbacks(
                    taskContext,
                    processItem: (idx, content) => {
                        this.Invoke((MethodInvoker)delegate
                        {
                            try
                            {
                                // 处理主表信息（索引0）或明细信息
                                if (idx == 0)
                                {
                                    ProcessAndBindJsonData(content, true);
                                }
                                else
                                {
                                    ProcessAndBindDetailData(content);
                                }
                            }
                            catch (Exception ex)
                            {
                                ShowWaitForm.SetWaitFormShowError($"处理页面 {idx + 1} 时出错: {ex.Message}");
                            }
                        });
                    },
                    updateProgress: idx => {
                        this.Invoke((MethodInvoker)delegate
                        {
                            ShowWaitForm.SetWaitFormDesption($"已处理 {idx + 1}/{totalRequests}...");
                        });
                    },
                    completeAll: () => {
                        this.Invoke((MethodInvoker)delegate
                        {
                            StopTimer();
                            ShowWaitForm.SetWaitFormStop("识别成功，请检查数据！");
                        });
                    },
                    handleError: (idx, errmessage) => {
                        this.Invoke((MethodInvoker)delegate
                        {
                            ShowWaitForm.SetWaitFormShowError($"PDF {idx + 1} 提取失败：{errmessage}");
                        });
                    }
                );

                // 分批处理请求
                await ProcessRequestsInBatches(requestQueue, processingData.CompletionSources,
                    maxConcurrentRequests, totalRequests);
            }
            catch (Exception ex)
            {
                StopTimer();
                ShowWaitForm.SetWaitFormStop($"批量处理PDF时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 准备所有页面的AI请求
        /// </summary>
        private List<ChatRequest> PrepareAllRequests(string firstPageContent)
        {
            List<ChatRequest> requests = new List<ChatRequest>();

            // 处理第一页主表信息
            int index = firstPageContent.IndexOf("核注清单明细");
            if (index != -1)
            {
                // 添加主表信息请求
                requests.Add(new ChatRequest
                {
                    Question = firstPageContent.Substring(0, index) + GetMainTablePrompt(),
                    Temperature = 0.1
                });

                // 添加第一页明细信息请求
                requests.Add(new ChatRequest
                {
                    Question = firstPageContent.Substring(index + "核注清单明细".Length) + GetDetailPrompt(),
                    Temperature = 0.3
                });
            }

            // 从第二页开始添加请求
            for (int i = 1; i < totalPages && !isEnd; i++)
            {
                string pageContent = ExtractPdfPageContent(currentFilePath, i).ToString();
                if (string.IsNullOrEmpty(pageContent.Trim()) || pageContent == " \r\n")
                {
                    isEnd = true;
                    break;
                }
                requests.Add(new ChatRequest
                {
                    Question = pageContent + GetDetailPrompt(),
                    Temperature = 0.3
                });
            }

            return requests;
        }

        /// <summary>
        /// 获取主表信息的提示词
        /// </summary>
        private string GetMainTablePrompt()
        {
            return @"
请仔细读取以上内容，注意我的\r、\n、\r、\n 换行空格信息。正式核扣时间填写日期时间值，不要填写别的值：
请获取其中的 核注清单编号、清单类型、报关单编号、正式核扣时间（时间类型)、关联报关单编号,
对于这些数值字段，请务必保持原始格式和精度，不要进行四舍五入或其他数值处理,
请以json的数据形式返回，格式如下：
{
    ""核注清单编号"":"""",
    ""清单类型"":"""",
    ""报关单编号"":"""",
    ""正式核扣时间"":"""",
    ""关联报关单编号"":""""
}";
        }

        /// <summary>
        /// 初始化处理数据所需的数据结构
        /// </summary>
        private (TaskCompletionSource<string>[] CompletionSources, string[] Results,
            bool[] ResultReady, object LockObj, int NextIndexToProcess)
            InitializeProcessingData(int totalRequests)
        {
            // 创建任务完成源数组
            var completionSources = Enumerable.Range(0, totalRequests)
                .Select(_ => new TaskCompletionSource<string>())
                .ToArray();

            // 创建结果存储数组（为了兼容性保留，但TaskSequenceHelper会处理顺序）
            string[] results = new string[totalRequests];
            bool[] resultReady = new bool[totalRequests];

            // 创建锁对象和处理索引
            object lockObj = new object();
            int nextIndexToProcess = 0;

            return (completionSources, results, resultReady, lockObj, nextIndexToProcess);
        }



        /// <summary>
        /// 分批处理请求
        /// </summary>
        private async Task ProcessRequestsInBatches(
            ConcurrentQueue<(int index, ChatRequest request)> requestQueue,
            TaskCompletionSource<string>[] completionSources,
            int maxConcurrentRequests, int totalRequests)
        {
            int batchSize = Math.Min(maxConcurrentRequests, totalRequests);
            int totalBatches = (int)Math.Ceiling((double)totalRequests / batchSize);

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
            {
                // 启动当前批次的工作线程
                int currentBatchSize = Math.Min(batchSize, totalRequests - batchIndex * batchSize);

                await Task.WhenAll(Enumerable.Range(0, currentBatchSize)
                    .Select(_ => ProcessRequestsAsync(requestQueue, completionSources)));

                // 批次之间添加短暂延迟，避免服务端限流
                if (batchIndex < totalBatches - 1)
                {
                    await Task.Delay(500);
                }
            }
        }



        /// <summary>
        /// 处理请求队列中的任务
        /// </summary>
        /// <param name="requestQueue">请求队列</param>
        /// <param name="completionSources">任务完成源数组</param>
        /// <returns>处理任务</returns>
        private async Task ProcessRequestsAsync(
            ConcurrentQueue<(int index, ChatRequest request)> requestQueue,
            TaskCompletionSource<string>[] completionSources)
        {
            while (requestQueue.TryDequeue(out var item))
            {
                try
                {
                    // 添加小延迟，避免请求过于密集导致服务端限流
                    await Task.Delay(100);

                    // 记录开始时间
                    //var requestStartTime = DateTime.Now;

                    // 发送AI请求
                    string content = await ai.SendMessageBatchStringAsync(item.request);

                    // 计算耗时并记录
                    //var elapsed = DateTime.Now - requestStartTime;
                    //LogRequestTime(item.index, elapsed);

                    // 设置结果
                    completionSources[item.index].SetResult(content);
                }
                catch (Exception ex)
                {
                    // 对于异常，直接设置异常
                    completionSources[item.index].SetException(ex);
                }
            }
        }

        /// <summary>
        /// 记录请求耗时
        /// </summary>
        /// <param name="requestIndex">请求索引</param>
        /// <param name="elapsed">耗时</param>
        private void LogRequestTime(int requestIndex, TimeSpan elapsed)
        {
            ShowWaitForm.SetWaitFormShowError(
                $"请求 {requestIndex + 1} 耗时: {elapsed.TotalSeconds:F2} 秒 " +
                $"({elapsed.Hours:D2}:{elapsed.Minutes:D2}:{elapsed.Seconds:D2})");
        }

        /// <summary>
        /// 获取明细数据提取的提示词
        /// </summary>
        /// <returns>提示词文本</returns>
        private string GetDetailPrompt()
        {
            return @"
请精确提取以下明细数据，特别注意数字的准确识别：
1. 请仔细检查所有数值字段，特别是申报数量、单价、总价等数字；
2. 注意区分相似的数字，如20000和2000，确保不要漏读数字位数；
3. 对于每个数值，请逐位检查，确保完全准确提取，小数点合理性，避免常识性错误；
4. 不进行任何四舍五入或数值近似处理；
5. 提取字段：商品序号、备案序号、商品料号、商品编码、商品名称、申报计量单位、申报数量、单价、总价、币制、法定计量单位、法定数量、第二法定计量单位、第二法定数量、原产国；
6. 返回json格式
请以JSON数组格式返回，并确保数值字段的完整性和准确性：
举个例子【输入】

                 59S
                 16A                        59S16A
                        853890                            2351    0.23  4237     欧 千 10.7                                        全
      3  184  -                    卡圈        -      个                                                    德国  中国
                         0000                               0       41      .6     元 克  8                                         免
                 1M4                        1M4/40
                 /40
                  Z
举个例子【输出】
[{
    ""商品序号"": 3,
    ""备案序号"": 184,
    ""商品料号"": ""59S16A-1M4/40Z"",
    ""商品编码"": ""8538900000"",
    ""商品名称"": ""卡圈"",
    ""申报计量单位"": ""个"",
    ""申报数量"": 23510,
    ""单价"": 0.2341,
    ""总价"": 4237.6,
    ""币制"": ""欧元"",
    ""法定计量单位"": ""千克"",
    ""法定数量"": 10.78,
    ""第二法定计量单位"": """",
    ""第二法定数量"":,
    ""原产国"": ""德国""
}]";
        }



        // 处理单个明细项
        private void ProcessDetailItem(dynamic item)
        {
            DataRow row = detailDT.NewRow();
            // 字符串字段处理（自动Trim，null/异常时返回空字符串）
            string GetStr(string field)
            {
                try { return (item[field]?.ToString() ?? "").Trim(); }
                catch { return ""; }
            }

            // 数值字段处理（null/异常时返回DBNull.Value）
            object GetNum(object field)
            {
                try
                {
                    var value = item[field];
                    if (value == null) return DBNull.Value;

                    // 尝试转换为数值
                    if (decimal.TryParse(value.ToString(), out decimal num))
                        return num;

                    return DBNull.Value;
                }
                catch
                {
                    return DBNull.Value;
                }
            }
            // 避免类型转换错误
            row["商品序号"] = GetStr("商品序号");
            row["备案序号"] = GetNum("备案序号");
            row["商品料号"] = GetStr("商品料号");
            row["商品编码"] = GetStr("商品编码");
            row["商品名称"] = GetStr("商品名称");
            row["申报计量单位"] = GetStr("申报计量单位");
            row["申报数量"] = GetNum("申报数量");
            row["单价"] = GetNum("单价"); ;
            row["总价"] = GetNum("总价"); ;
            row["币制"] = GetStr("币制"); ;
            row["法定计量单位"] = GetStr("法定计量单位");
            row["法定数量"] = GetNum("法定数量");
            row["第二法定计量单位"] = GetStr("第二法定计量单位");
            row["第二法定数量"] = GetNum("第二法定数量");
            row["原产国"] = GetStr("原产国");
            detailDT.Rows.Add(row);
        }


        #region JSON数据处理

        /// <summary>
        /// 处理主表JSON数据并绑定到控件
        /// </summary>
        /// <param name="jsonText">AI返回的JSON文本</param>
        /// <param name="isFirstPage">是否为第一页数据</param>
        private void ProcessAndBindJsonData(string jsonText, bool isFirstPage = true)
        {
            try
            {
                // 提取JSON部分
                string jsonPart = ExtractJsonObject(jsonText);

                // 解析JSON数据
                dynamic jsonData = JsonConvert.DeserializeObject(jsonPart);

                if (jsonData == null)
                {
                    throw new Exception("JSON数据解析失败");
                }

                // 绑定数据到界面控件
                if (isFirstPage)
                {
                    BindMainTableData(jsonData);
                }

                // 更新数据源和刷新网格
                gd.DataSource = detailDT;
                gdv.RefreshData();
            }
            catch (Exception ex)
            {
                throw new Exception($"处理主表数据时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从文本中提取JSON对象
        /// </summary>
        /// <param name="text">包含JSON的文本</param>
        /// <returns>提取的JSON字符串</returns>
        private string ExtractJsonObject(string text)
        {
            int startIndex = text.IndexOf('{');
            int endIndex = text.LastIndexOf('}');

            if (startIndex < 0 || endIndex <= startIndex)
            {
                throw new Exception("未找到有效的JSON数据");
            }

            return text.Substring(startIndex, endIndex - startIndex + 1);
        }

        /// <summary>
        /// 从文本中提取JSON数组
        /// </summary>
        /// <param name="text">包含JSON数组的文本</param>
        /// <returns>提取的JSON数组字符串</returns>
        private string ExtractJsonArray(string text)
        {
            int startBrace = text.IndexOf('{');
            int startBracket = text.IndexOf('[');

            // 确定起始符号（优先处理数组格式）
            int startIndex = startBracket >= 0 ? startBracket : startBrace;
            if (startIndex < 0) throw new Exception("未找到有效的 JSON 数据");

            // 自动包装单个对象为数组
            if (text[startIndex] == '{')
            {
                return $"[{text.Substring(startIndex, text.LastIndexOf('}') - startIndex + 1)}]";
            }

            // 直接返回数组
            return text.Substring(startIndex, text.LastIndexOf(']') - startIndex + 1);
        }

        /// <summary>
        /// 绑定主表数据到界面控件
        /// </summary>
        /// <param name="jsonData">解析后的JSON数据</param>
        private void BindMainTableData(dynamic jsonData)
        {
            // 设置主要信息到文本框
            if (jsonData.核注清单编号 != null)
                txtHZQDBH.Text = jsonData.核注清单编号.ToString();

            if (jsonData.清单类型 != null)
                txtQDLX.Text = jsonData.清单类型.ToString();

            if (jsonData.报关单编号 != null)
                txtBGDBH.Text = jsonData.报关单编号.ToString();

            if (jsonData.正式核扣时间 != null)
            {
                if (DateTime.TryParse(jsonData.正式核扣时间.ToString(), out DateTime parsedDate))
                {
                    txtZSHKSJ.EditValue = parsedDate;
                }
            }

            if (jsonData.关联报关单编号 != null)
                txtGLBGDBH.Text = jsonData.关联报关单编号.ToString();

            // 设置操作人和操作时间
            txtCZR.Text = ICF.ISO.UNAME;
            txtCZSJ.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        /// <summary>
        /// 处理明细JSON数据并绑定到GridView
        /// </summary>
        /// <param name="jsonText">AI返回的JSON文本</param>
        private void ProcessAndBindDetailData(string jsonText)
        {
            try
            {
                // 尝试从文本中提取JSON数组部分
                string jsonPart = ExtractJsonArray(jsonText);

                // 解析JSON数据为明细列表
                var detailList = JsonConvert.DeserializeObject<List<dynamic>>(jsonPart);

                if (detailList == null || detailList.Count == 0)
                {
                    throw new Exception("未找到有效的明细数据");
                }

                // 处理每个明细项
                foreach (var item in detailList)
                {
                    ProcessDetailItem(item);
                }

                // 自动调整列宽
                gdv.BestFitColumns();
            }
            catch (Exception ex)
            {
                throw new Exception($"处理明细数据时出错: {ex.Message}");
            }
        }

        #endregion


        #region PDF内容提取

        /// <summary>
        /// 使用Spire.PDF提取指定页的PDF文件内容
        /// </summary>
        /// <param name="filePath">PDF文件路径</param>
        /// <param name="pageIndex">页码索引（从0开始）</param>
        /// <returns>提取的文本内容</returns>
        private StringBuilder ExtractPdfPageContent(string filePath, int pageIndex)
        {
            StringBuilder sb = new StringBuilder();

            try
            {
                // 创建PdfDocument对象
                using (PdfDocument pdf = new PdfDocument())
                {
                    // 加载PDF文件
                    pdf.LoadFromFile(filePath);

                    // 确保页码索引有效
                    if (pageIndex < 0 || pageIndex >= pdf.Pages.Count)
                    {
                        return sb; // 返回空内容
                    }

                    // 获取指定页面
                    PdfPageBase page = pdf.Pages[pageIndex];

                    // 提取页面文本
                    string text = page.ExtractText();

                    // 检查是否包含结束标记
                    if (ContainsEndMarker(text, out string processedText))
                    {
                        // 设置当前页为最后一页，这样就不会继续处理后续页面
                        isEnd = true;
                        text = processedText;
                    }

                    sb.AppendLine(text);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断流程
                ShowWaitForm.SetWaitFormShowError($"提取PDF第{pageIndex + 1}页时出错: {ex.Message}");
            }

            return sb;
        }

        /// <summary>
        /// 检查文本是否包含结束标记
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <param name="processedText">处理后的文本</param>
        /// <returns>是否包含结束标记</returns>
        private bool ContainsEndMarker(string text, out string processedText)
        {
            processedText = text;

            // 检查是否包含"简单加工成品"标记
            int index = text.IndexOf("简单加工成品");
            if (index != -1)
            {
                // 只保留标记之前的内容
                processedText = text.Substring(0, index);
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取PDF文件的总页数
        /// </summary>
        /// <param name="filePath">PDF文件路径</param>
        /// <returns>PDF总页数</returns>
        private int GetPdfPageCount(string filePath)
        {
            try
            {
                // 创建PdfDocument对象
                using (PdfDocument pdf = new PdfDocument())
                {
                    // 加载PDF文件
                    pdf.LoadFromFile(filePath);
                    // 返回总页数
                    return pdf.Pages.Count;
                }
            }
            catch (Exception ex)
            {
                ShowWaitForm.SetWaitFormShowError($"获取PDF页数时出错: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #endregion

        #region 界面操作

        /// <summary>
        /// 清空按钮点击事件
        /// </summary>
        private void btnClear_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Clear();
        }

        /// <summary>
        /// 清空所有数据和界面控件
        /// </summary>
        private void Clear()
        {
            // 清空数据源
            gd.DataSource = null;

            // 清空文本框
            ClearTextBoxes();

            // 清空数据表
            ClearDataTables();

            // 重置状态
            ResetStatus();

            // 重置按钮状态
            UpdateButtonStatus(false);

            // 停止计时器并重置时间显示
            StopTimer();
            txtTime.Text = "00:00:00";
        }

        /// <summary>
        /// 清空所有文本框
        /// </summary>
        private void ClearTextBoxes()
        {
            txtHZQDBH.Text = "";
            txtQDLX.Text = "";
            txtBGDBH.Text = "";
            txtZSHKSJ.Text = "";
            txtGLBGDBH.Text = "";
            txtCZR.Text = "";
            txtCZSJ.Text = "";
            txtHL.Text = "";
            lblRemark.Text = "";
            jkBZ = "";
            ckBZ = "";
            txtID.Text = "";
        }

        /// <summary>
        /// 清空数据表
        /// </summary>
        private void ClearDataTables()
        {
            if (detailDT != null)
                detailDT.Clear();

            if (dt != null)
                dt.Clear();
        }

        /// <summary>
        /// 重置状态变量
        /// </summary>
        private void ResetStatus()
        {
            isEnd = false;
            isSave = false;
            lblZT.Text = "未核销";
            lblZT.Visible = false;
            txtHL.Enabled = false;
        }

        /// <summary>
        /// 更新按钮状态
        /// </summary>
        /// <param name="isChecked">是否已核销</param>
        private void UpdateButtonStatus(bool isChecked)
        {
            btnHD.Enabled = !isChecked;
            btnDel.Enabled = isSave;
            ckbEdit.Enabled = !isChecked;
            ckbEdit.Checked = isChecked;
        }

        #endregion

        private void btnReturn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DoReturn();
        }

        private void btnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {

                HdSave();
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void HdSave()
        {
            try
            {

                bs.EndEdit();
                gdv.CloseEditor();
                gdv.UpdateCurrentRow();
                if (string.IsNullOrEmpty(txtHZQDBH.Text))
                {
                    return;
                }
                if (detailDT == null || detailDT.Rows.Count == 0)
                {
                    return;
                }
                if (lblZT.Text == "已核销")
                {
                    throw new Exception("已核销，不允许修改！");
                }

                //检查他们的币制是否相同
                //如果存在多个，则不允许核销，进行提醒
                string checkBZ = "";
                bool isMultipleCurrency = false;

                foreach (DataRow row in detailDT.Rows)
                {
                    if (row["币制"] == DBNull.Value || string.IsNullOrEmpty(row["币制"].ToString()))
                        continue;

                    string currentBZ = row["币制"].ToString();

                    if (string.IsNullOrEmpty(checkBZ))
                    {
                        checkBZ = currentBZ;
                    }
                    else if (checkBZ != currentBZ)
                    {
                        isMultipleCurrency = true;
                        break;
                    }
                }

                if (isMultipleCurrency)
                {
                    ICF.ISD.ShowError("当前出口核注清单中存在多种不同币制，不允许核销！");
                    return;
                }


                ShowWaitForm.WaitFormShow(this, "正在核销...");
                //将表头保存到：出口核注清单表
                string sql = "select * from 出口核注清单表 where 核注清单编号 = '" + txtHZQDBH.Text + "' and  ID != '" + txtID.Text + "'";
                DataTable checkHZQD = SqlHelper.FillDataTable(sql);
                if (checkHZQD.Rows.Count > 0)
                {
                    throw new Exception("当前识别的出口核注清单已经存在！");
                }
                sql = "select * from 出口核注清单表 where ID = '" + txtID.Text + "'";
                DataTable ckhzDT = SqlHelper.FillDataTable(sql);
                DataRow dr;
                string id = txtID.Text;
                if (ckhzDT.Rows.Count > 0)
                {
                    dr = ckhzDT.Rows[0];
                }
                else
                {
                    dr = ckhzDT.NewRow();

                    DateTime currentDate = DateTime.Now;
                    string strId = "CKHZ" + (currentDate.Year % 100).ToString("00") + currentDate.Month.ToString("00");
                    //获取进口核注清单表的MaxId
                    string maxID = SqlHelper.GetMaxID("ID", "出口核注清单表", "where ID like '" + strId + "%'");
                    if (maxID == "")
                    {
                        id = strId + "001";
                    }
                    else
                    {
                        string lastFourDigits = maxID.Substring(maxID.Length - 3);
                        // 将截取的字符串转换为数字
                        int lastFourNumber = int.Parse(lastFourDigits) + 1;
                        // 拼接最终结果
                        id = strId + lastFourNumber.ToString("D3");
                    }

                    dr["ID"] = id;

                    ckhzDT.Rows.Add(dr);
                }
                string jkSql = "select * from 进口核注清单明细表 where 1 = 0 ";
                DataTable jkDT = SqlHelper.FillDataTable(jkSql);

                string jlSql = "select * from 核注清单记录表 where 1 = 0 ";
                DataTable jlDT = SqlHelper.FillDataTable(jlSql);

                dr["核注清单编号"] = txtHZQDBH.Text;

                //对核注清单编号进行赋值，同时检查是否核销条件
                string erroMsg = "";
                foreach (DataRow row in detailDT.Rows)
                {
                    if (row["备案序号"] == DBNull.Value || string.IsNullOrEmpty(row["备案序号"].ToString()))
                    {
                        throw new Exception("请输入备案序号");
                    }

                    string msg = "";
                    row["上级ID"] = id;

                    string jkSQL = "select * from 进口核注清单明细表 where 备案序号 = '" + row["备案序号"] + "'";
                    DataTable jkinfoDT = SqlHelper.FillDataTable(jkSQL);
                    if (jkinfoDT.Rows.Count > 0)
                    {
                        DataRow jkRow = jkinfoDT.Rows[0];
                        decimal jkdj = jkRow["单价"] != DBNull.Value ? Convert.ToDecimal(jkRow["单价"]) : 0;
                        decimal ckdj = row["单价"] != DBNull.Value ? Convert.ToDecimal(row["单价"]) : 0;


                        if (jkRow["币制"].ToString() == row["币制"].ToString())
                        {
                            if (jkdj > ckdj)
                            {
                                msg += " 单价不匹配；";
                            }
                        }
                        else
                        {
                            //币种不同，则进行汇率转换
                            ShowWaitForm.SetWaitFormShowError("检查到币制不同，需要进行汇率转换！");
                            if (!string.IsNullOrEmpty(txtHL.Text) && Convert.ToDecimal(txtHL.Text) != 0)
                            {
                                //进行汇率转换判断，汇率栏位，出口币制，折算成进口币制是；
                                decimal zhDJ = ckdj * Convert.ToDecimal(txtHL.Text);
                                if (jkdj > zhDJ)
                                {
                                    msg += " 单价不匹配；";
                                }
                                else
                                {
                                    ShowWaitForm.SetWaitFormShowError($"备案序号:" +
                                        $"{ row["备案序号"]}:出口币制：{row["币制"] } 折算成 进口币制：{jkRow["币制"]}，单价：{zhDJ}！");
                                }
                            }
                            else
                            {
                                erroMsg = "币制不同，未检查到输入的汇率信息，请输入汇率！";
                                jkBZ = jkRow["币制"].ToString();
                                ckBZ = row["币制"].ToString();
                                lblRemark.Text = $"1{ckBZ} = 【 {txtHL.Text} 】{jkBZ}";
                                txtHL.Enabled = true;
                                break;
                            }

                        }

                        if (jkRow["商品料号"].ToString() != row["商品料号"].ToString())
                        {
                            msg += " 商品料号不匹配；";
                        }
                        if (jkRow["原产国"].ToString() != row["原产国"].ToString())
                        {
                            msg += " 原产国不匹配；";
                        }
                        // 计算剩余申报数量
                        if (jkRow["剩余申报数量"] != DBNull.Value && row["申报数量"] != DBNull.Value)
                        {
                            decimal remainingQty = Convert.ToDecimal(jkRow["剩余申报数量"]) - Convert.ToDecimal(row["申报数量"]);
                            if (remainingQty < 0)
                            {
                                msg += "剩余申报数量不足；";
                            }
                            else
                            {
                                jkRow["剩余申报数量"] = remainingQty;
                                foreach (DataRow row2 in detailDT.Rows)
                                {
                                    if (row2["备案序号"].ToString() == row["备案序号"].ToString())
                                    {
                                        row2["剩余申报数量"] = remainingQty;
                                    }
                                }
                                jkRow["已核销申报数量"] = row["申报数量"];
                            }

                        }

                        // 计算剩余法定数量 - 确保法定计量单位一致
                        if (jkRow["剩余法定数量"] != DBNull.Value && row["法定数量"] != DBNull.Value)
                        {
                            if (jkRow["法定计量单位"].ToString() == row["法定计量单位"].ToString())
                            {
                                decimal remainingLegalQty = Convert.ToDecimal(jkRow["剩余法定数量"]) - Convert.ToDecimal(row["法定数量"]);
                                if (remainingLegalQty < 0)
                                {
                                    msg += "剩余法定数量不足！";
                                }
                                else
                                {
                                    jkRow["剩余法定数量"] = remainingLegalQty;
                                    foreach (DataRow row2 in detailDT.Rows)
                                    {
                                        if (row2["备案序号"].ToString() == row["备案序号"].ToString())
                                        {
                                            row2["剩余法定数量"] = remainingLegalQty;
                                        }
                                    }
                                    jkRow["已核销法定数量"] = row["法定数量"];
                                }
                            }
                            else
                            {
                                msg += "法定计量单位不同！";
                            }

                        }

                        // 计算剩余第二法定数量 - 确保第二法定计量单位一致
                        if (jkRow["剩余第二法定数量"] != DBNull.Value && row["第二法定数量"] != DBNull.Value)
                        {
                            if (jkRow["第二法定计量单位"].ToString() == row["第二法定计量单位"].ToString())
                            {
                                decimal remainingSecondLegalQty = Convert.ToDecimal(jkRow["剩余第二法定数量"]) - Convert.ToDecimal(row["第二法定数量"]);
                                if (remainingSecondLegalQty < 0)
                                {
                                    msg += "剩余第二法定数量不足！";
                                }
                                else
                                {
                                    jkRow["剩余第二法定数量"] = remainingSecondLegalQty;
                                    foreach (DataRow row2 in detailDT.Rows)
                                    {
                                        if (row2["备案序号"].ToString() == row["备案序号"].ToString())
                                        {
                                            row2["剩余第二法定数量"] = remainingSecondLegalQty;
                                        }
                                    }
                                    jkRow["已核销第二法定数量"] = row["第二法定数量"];
                                }
                            }
                            else
                            {
                                msg += "第二法定计量单位不同！";
                            }


                        }

                        if (!string.IsNullOrEmpty(msg))
                        {
                            ShowWaitForm.SetWaitFormShowError($"备案序号: { row["备案序号"]},{msg}； ");
                            erroMsg += $"备案序号: { row["备案序号"]}, 单价: { row["单价"]}, 商品料号: { row["商品料号"]},{msg}； ";
                        }
                        else
                        {
                            row["进口核注ID"] = jkinfoDT.Rows[0]["ID"];
                            DataRow jlDr = jlDT.NewRow();
                            jlDr["进口核注清单编号"] = jkRow["ID"];
                            jlDr["出口核注清单编号"] = id;
                            jlDr["备案序号"] = row["备案序号"];
                            jlDr["内容"] = $"申报数量：{row["申报数量"]},法定数量：{row["法定数量"]},第二法定数量：{row["第二法定数量"]}";
                            jlDr["操作人"] = ICF.ISO.UNAME;
                            jlDr["操作时间"] = DateTime.Now;
                            jlDT.Rows.Add(jlDr);
                            jkDT.Rows.Add(jkRow.ItemArray);
                        }

                    }
                    else
                    {
                        msg += $"未找到匹配的进口核注清单明细记录";
                        ShowWaitForm.SetWaitFormShowError($"备案序号: { row["备案序号"]},{msg}； ");
                        erroMsg += msg;
                    }




                }
                if (!string.IsNullOrEmpty(erroMsg))
                {
                    ShowWaitForm.SetWaitFormStop("核销失败。");
                    return;
                }

                dr["清单类型"] = txtQDLX.Text;
                dr["报关单编号"] = txtBGDBH.Text;
                if (!string.IsNullOrEmpty(txtZSHKSJ.Text))
                {
                    dr["正式核扣时间"] = txtZSHKSJ.Text;
                }
                else
                {
                    dr["正式核扣时间"] = DBNull.Value;
                }
                dr["操作人"] = txtCZR.Text;
                dr["操作时间"] = txtCZSJ.Text;
                dr["状态"] = "未核销";
                if (!string.IsNullOrEmpty(txtHL.Text))
                {
                    dr["汇率"] = txtHL.Text;
                }

                dr["备注"] = lblRemark.Text;
                if (detailDT.GetChanges() != null && detailDT.GetChanges().Rows.Count > 0)
                {
                    SqlHelper.UpdateDataTable("select * from 出口核注清单明细表 where 核注清单编号 = '" + txtHZQDBH.Text + "'", detailDT.GetChanges());
                }

                List<string> strList = new List<string>();
                foreach (DataRow item in jkDT.Rows)
                {
                    strList.Add($@"UPDATE 进口核注清单明细表 set
剩余申报数量 = '{item["剩余申报数量"]}',
剩余法定数量 = '{item["剩余法定数量"]}',
剩余第二法定数量 = '{item["剩余第二法定数量"]}',
已核销申报数量 = 已核销申报数量 + {item["已核销申报数量"]},
已核销法定数量 = 已核销法定数量 + {item["已核销法定数量"]},
已核销第二法定数量 = 已核销第二法定数量 + {item["已核销第二法定数量"]}
where ID = '{item["ID"]}'
");
                }
                SqlHelper.ExecuteNonSQlList(strList);
                SqlHelper.UpdateDataTable(jlSql, jlDT);
                dr["状态"] = "已核销";
                SqlHelper.UpdateDataTable(sql, ckhzDT);
                txtID.Text = id;

                LoadData();
                ShowWaitForm.SetWaitFormStop("核销成功！");



            }
            catch (Exception)
            {
                if (ShowWaitForm.WaitMessageForm != null && !ShowWaitForm.WaitMessageForm.IsDisposed)
                    ShowWaitForm.CloseWaitForm();
                throw;
            }

        }

        private void LoadData()
        {
            List<string> sqlList = new List<string>();
            string sql = "select * from 出口核注清单表 where ID = '" + txtID.Text + "'";
            string sql2 = "select * from 出口核注清单明细表 where 上级ID = '" + txtID.Text + "'";
            sqlList.Add(sql);
            sqlList.Add(sql2);
            using (DataSet ds = SqlHelper.FillDataSet(sqlList))
            {
                dt = ds.Tables[0];
                if (dt.Rows.Count > 0)
                {
                    lblZT.Visible = true;
                    lblZT.Text = dt.Rows[0]["状态"].ToString();
                    if (lblZT.Text == "已核销")
                    {
                        btnHD.Enabled = false;
                        ckbEdit.Enabled = false;
                        ckbEdit.Checked = false;
                        btnDel.Enabled = true; // 允许删除已核销的记录
                        gdv.OptionsBehavior.Editable = false;
                    }
                }
                detailDT = ds.Tables[1];
                initialDetailDT = ds.Tables[1];
            };
            if (dt.Rows.Count > 0)
            {
                isSave = true;
            }
            gd.DataSource = detailDT;
            gdv.BestFitColumns();

        }

        private void frmPDFCK_Load(object sender, EventArgs e)
        {
            try
            {
                if (vInParam != null)
                {
                    txtID.Text = ((ArrayList)vInParam)[0].ToString();
                }
                LoadData();
                bs.DataSource = dt;
                DataBind();

                // 初始化计时器
                timer.Interval = 1000; // 每秒更新一次
                timer.Tick += Timer_Tick;
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        #region 计时器管理

        /// <summary>
        /// 计时器Tick事件处理
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            if (isTimerRunning)
            {
                // 计算已经过去的时间
                elapsedTime = DateTime.Now - startTime;

                // 更新时间显示，格式为 HH:mm:ss
                UpdateTimerDisplay();
            }
        }

        /// <summary>
        /// 更新计时器显示
        /// </summary>
        private void UpdateTimerDisplay()
        {
            txtTime.Text = elapsedTime.ToString(@"hh\:mm\:ss");
        }

        /// <summary>
        /// 开始计时
        /// </summary>
        private void StartTimer()
        {
            // 重置计时器状态
            startTime = DateTime.Now;
            elapsedTime = TimeSpan.Zero;
            isTimerRunning = true;

            // 启动计时器
            timer.Start();

            // 初始显示
            txtTime.Text = "00:00:00";
        }

        /// <summary>
        /// 停止计时
        /// </summary>
        private void StopTimer()
        {
            if (isTimerRunning)
            {
                // 停止计时器
                timer.Stop();
                isTimerRunning = false;

                // 计算最终时间
                elapsedTime = DateTime.Now - startTime;
                UpdateTimerDisplay();
            }
        }

        #endregion


        private void btnHD_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtHZQDBH.Text))
                {
                    ICF.ISD.ShowError("核注清单编号不能为空！");
                    return;
                }
                
                HdSave();
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void btnDel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //删除
            try
            {
                if (!isSave)
                {
                    return;
                }
                DialogResult dlgResult = MessageBox.Show("确定要删除该核注清单吗？已核销的数量会进行回退", "确认", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                if (dlgResult == DialogResult.OK)
                {
                    ShowWaitForm.WaitFormShow(this, "正在处理...");

                    if (lblZT.Text == "已核销")
                    {
                        // 允许删除，但需要将数量进行回退
                        RollbackQuantities();
                    }

                    // 执行删除操作
                    string delSql = "delete from 出口核注清单表 where ID = '" + txtID.Text + "'";
                    string delDetailSql = "delete from 出口核注清单明细表 where 上级ID = '" + txtID.Text + "'";

                    List<string> sqlList = new List<string>() {
                        delSql,delDetailSql
                    };

                    SqlHelper.ExecuteNonSQlTran(sqlList);
                    ShowWaitForm.SetWaitFormStop("删除成功！");
                    ICF.ISD.ShowTips("删除成功！");
                    Clear();
                }


            }
            catch (Exception exc)
            {
                if (ShowWaitForm.WaitMessageForm != null && !ShowWaitForm.WaitMessageForm.IsDisposed)
                    ShowWaitForm.CloseWaitForm();
                ICF.ISD.ShowError(exc.Message);
            }
        }

        /// <summary>
        /// 回退已核销的数量到进口核注清单
        /// </summary>
        private void RollbackQuantities()
        {
            try
            {
                // 获取当前出口核注清单的明细数据
                string sql = "select * from 出口核注清单明细表 where 上级ID = '" + txtID.Text + "'";
                DataTable detailTable = SqlHelper.FillDataTable(sql);

                if (detailTable == null || detailTable.Rows.Count == 0)
                {
                    return;
                }

                List<string> updateStatements = new List<string>();

                // 处理每一条明细记录
                foreach (DataRow row in detailTable.Rows)
                {
                    // 检查是否有进口核注ID
                    if (row["进口核注ID"] == DBNull.Value || string.IsNullOrEmpty(row["进口核注ID"].ToString()))
                    {
                        continue;
                    }

                    string jkID = row["进口核注ID"].ToString();

                    // 构建更新语句，将数量加回到进口核注清单明细表
                    StringBuilder updateSql = new StringBuilder();
                    updateSql.Append("UPDATE 进口核注清单明细表 SET ");

                    // 申报数量回退
                    if (row["申报数量"] != DBNull.Value)
                    {
                        updateSql.Append("剩余申报数量 = 剩余申报数量 + " + row["申报数量"] + ",已核销申报数量 = 已核销申报数量 - " + row["申报数量"]);
                    }

                    // 法定数量回退
                    if (row["法定数量"] != DBNull.Value)
                    {
                        if (updateSql.ToString().EndsWith("SET "))
                            updateSql.Append("剩余法定数量 = 剩余法定数量 + " + row["法定数量"] + ",已核销法定数量 = 已核销法定数量 - " + row["法定数量"]);
                        else
                            updateSql.Append(", 剩余法定数量 = 剩余法定数量 + " + row["法定数量"] + ",已核销法定数量 = 已核销法定数量 - " + row["法定数量"]);
                    }

                    // 第二法定数量回退
                    if (row["第二法定数量"] != DBNull.Value)
                    {
                        if (updateSql.ToString().EndsWith("SET "))
                            updateSql.Append("剩余第二法定数量 = 剩余第二法定数量 + " + row["第二法定数量"] + ",已核销第二法定数量 = 已核销第二法定数量 - " + row["第二法定数量"]);
                        else
                            updateSql.Append(", 剩余第二法定数量 = 剩余第二法定数量 + " + row["第二法定数量"] + ",已核销第二法定数量 = 已核销第二法定数量 - " + row["第二法定数量"]);
                    }

                    // 添加WHERE条件
                    updateSql.Append(" WHERE ID = '" + jkID + "'");

                    // 如果有需要更新的字段，添加到更新语句列表
                    if (!updateSql.ToString().EndsWith("SET "))
                    {
                        updateStatements.Add(updateSql.ToString());
                    }

                    // 添加记录到核注清单记录表
                    string recordSql = $@"INSERT INTO 核注清单记录表 (进口核注清单编号, 出口核注清单编号, 备案序号, 内容, 操作人, 操作时间)
                    VALUES ('{jkID}', '{row["上级ID"]}', '{row["备案序号"]}', '删除出口核注清单，回退数量：申报数量 {row["申报数量"]}, 法定数量 {row["法定数量"]}, 第二法定数量 {row["第二法定数量"]}', '{ICF.ISO.UNAME}', '{DateTime.Now}')";

                    updateStatements.Add(recordSql);
                }

                // 执行所有更新语句
                if (updateStatements.Count > 0)
                {
                    SqlHelper.ExecuteNonSQlList(updateStatements);
                    ShowWaitForm.SetWaitFormDesption("数量回退成功！");
                }
            }
            catch (Exception ex)
            {
                throw new Exception("回退数量时出错: " + ex.Message);
            }
        }

        private void btnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void ckbEdit_CheckedChanged(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gdv.OptionsBehavior.Editable = ckbEdit.Checked;
        }

        /// <summary>
        /// 修改汇率提示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtHL_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            lblRemark.Text = $"1{ckBZ} = 【 {e.NewValue} 】{jkBZ}";
        }

        private void gdv_KeyDown(object sender, KeyEventArgs e)
        {
            int iRowHandle = -1;
            if (!ckbEdit.Checked)
            {
                return;
            }
            try
            {

                if (e.KeyCode == Keys.Delete && gdv.OptionsBehavior.ReadOnly == false)
                {
                    iRowHandle = gdv.FocusedRowHandle;

                    if (iRowHandle < 0)
                        return;

                    gdv.DeleteRow(iRowHandle);
                }
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }
    }
}
